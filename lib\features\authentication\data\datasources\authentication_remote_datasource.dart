import 'package:cal/common/consts/app_keys.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../models/auth_token_model.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cal/core/network/api_handler.dart';
import 'package:cal/core/network/http_client.dart';
import 'package:cal/common/utils/shared_preferences_helper.dart';

// Remote Data Source for Authentication
abstract class AuthenticationRemoteDataSource {
  Future<AuthTokenModel> signInWithApple();
  Future<AuthTokenModel> signInWithGoogle();
  Future<void> signOut();
}

class AuthenticationRemoteDataSourceImpl with ApiHandler implements AuthenticationRemoteDataSource {
  final FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;
  final HTTPClient httpClient;

  AuthenticationRemoteDataSourceImpl({
    required FirebaseAuth firebaseAuth,
    required GoogleSignIn googleSignIn,
    required this.httpClient,
  })  : _firebaseAuth = firebaseAuth,
        _googleSignIn = googleSignIn;

  @override
  Future<AuthTokenModel> signInWithApple() async {
    try {
      // Get Apple ID credential
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // Create Firebase OAuth credential
      final oauthCredential = OAuthProvider('apple.com').credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      // Sign in with Firebase
      final userCredential = await _firebaseAuth.signInWithCredential(oauthCredential);
      final user = userCredential.user;

      if (user == null) {
        throw Exception('User not found after Apple sign-in');
      }

      // Get Firebase ID token
      final idTokenResult = await user.getIdTokenResult();
      return AuthTokenModel(
        accessToken: idTokenResult.token!,
        expiresAt: idTokenResult.expirationTime!,
      );
    } catch (e) {
      throw Exception('Apple Sign-In failed: $e');
    }
  }

  @override
  Future<AuthTokenModel> signInWithGoogle() async {
    try {
      // Sign in with Google
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        throw Exception('Google Sign-In was cancelled by user');
      }

      // Get Google authentication details
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Create Firebase credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

// 1️⃣ Sign in with Firebase using Google
      print("🔄 Attempting Firebase Google Sign-In...");
      final userCredential = await _firebaseAuth.signInWithCredential(credential);

// 2️⃣ Extract user
      final user = userCredential.user;
      if (user == null) {
        print("❌ Firebase returned null user");
        throw Exception('User not found after Google sign-in');
      }
      print("✅ Firebase user signed in: ${user.email}");

// 3️⃣ Get Firebase ID token (not actually used here, but still fetched)
      final idTokenResult = await user.getIdTokenResult();
      print("🪪 Firebase ID Token fetched: ${idTokenResult.token?.substring(0, 10)}...");

// 4️⃣ Prepare the Google token payload for backend
      final googleTokenData = {'token': googleAuth.idToken};
      print("📦 Google token data prepared: $googleTokenData");

// 5️⃣ Call backend API
      print("🚀 Sending token to backend /api/auth/google...");
      final apiResult = await handleApiCall(
        apiCall: () => httpClient.post(
          '/api/auth/google',
          data: googleTokenData,
          headers: {'Content-Type': 'application/json'},
        ),
      );

// 6️⃣ Handle result
      apiResult.fold(
        (failure) {
          print("❌ Backend API call failed: $failure");
          throw Exception('API call failed during Google Sign-In: $failure');
        },
        (response) {
          print("🧾 Raw backend response data: $response");

          // ✅ Fix: response is already the parsed JSON map
          final token = response['token'];
          print("🔍 Extracted token from response: $token");

          if (token != null && token is String) {
            ShPH.saveData(key: AppKeys.token, value: token);
            print("💾 Token saved to storage.");
          } else {
            print("❌ Token not found or invalid.");
            throw Exception("Token not found in response.");
          }
        },
      );

      return AuthTokenModel(
        accessToken: idTokenResult.token!,
        expiresAt: idTokenResult.expirationTime!,
      );
    } catch (e) {
      throw Exception('Google Sign-In failed: $e');
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
      await _googleSignIn.signOut();
    } catch (e) {
      throw Exception('Sign-out failed: $e');
    }
  }
}
