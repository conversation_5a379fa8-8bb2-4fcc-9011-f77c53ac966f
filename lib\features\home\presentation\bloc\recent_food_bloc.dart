import 'dart:async';

import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/home/<USER>/repositories/food_repository.dart';
import 'package:cal/features/home/<USER>/usecases/get_daily_user_data_usecase.dart';
import 'package:cal/features/home/<USER>/usecases/update_daily_user_data_usecase.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/datasources/streak_local_data_source.dart';

part 'recent_food_event.dart';
part 'recent_food_state.dart';

@singleton
class RecentFoodBloc extends Bloc<RecentFoodEvent, RecentFoodState> {
  final FoodRepository foodRepository;
  final StreakLocalDataSource streakService;
  final GetDailyUserDataUseCase getDailyUserDataUseCase;
  final UpdateDailyUserDataUseCase updateDailyUserDataUseCase;

  RecentFoodBloc({
    required this.foodRepository,
    required this.streakService,
    required this.getDailyUserDataUseCase,
    required this.updateDailyUserDataUseCase,
  }) : super(const RecentFoodState()) {
    on<LoadFood>(_onLoadFood);
    on<AddFood>(_onAddFood);
    on<UpdateFood>(_onUpdateFood);
    on<ClearFood>(_onClearFood);
    on<DeleteFood>(_onDeleteFood);
  }

  Future<void> _onLoadFood(LoadFood event, Emitter<RecentFoodState> emit) async {
    emit(state.copyWith(status: RecentFoodStatus.loading));
    try {
      final meals = await foodRepository.getAllFood(event.date);

      emit(state.copyWith(status: RecentFoodStatus.success, foodList: meals));
    } catch (e) {
      emit(state.copyWith(status: RecentFoodStatus.failure, errorMessage: e.toString()));
    }
  }

  Future<void> _onAddFood(AddFood event, Emitter<RecentFoodState> emit) async {
    final currentList = [...state.foodList];
    final updatedList = [event.meal, ...currentList];
    if (event.meal.isLoading) {
      emit(state.copyWith(foodList: List.of(updatedList)));
    } else if (!event.meal.hasError) {
      await _saveAndEmit(event.meal, updatedList, emit);
      await _updateDailyUserData(
          event.meal.date!, event.meal.calories!.toDouble(), event.meal.carbs!, event.meal.protein!, event.meal.fat!);
    } else {
      emit(state.copyWith(foodList: List.of(updatedList)));
    }
  }

  Future<void> _onUpdateFood(UpdateFood event, Emitter<RecentFoodState> emit) async {
    final updatedList = state.foodList.map((food) {
      if (food.id == event.meal.id) return event.meal;
      return food;
    }).toList();

    emit(state.copyWith(status: RecentFoodStatus.success, foodList: List.of(updatedList)));
    if (!event.meal.hasError) {
      await foodRepository.updateMeal(event.meal);
      if (event.meal.date != null && !event.meal.isLoading) {
        await _updateDailyUserData(
          event.meal.date!,
          event.meal.calories?.toDouble() ?? 0.0,
          event.meal.carbs ?? 0.0,
          event.meal.protein ?? 0.0,
          event.meal.fat ?? 0.0,
        );
      }
    }
  }

  Future<void> _saveAndEmit(FoodModel meal, List<FoodModel> updatedList, Emitter<RecentFoodState> emit) async {
    await foodRepository.saveMeal(meal);
    emit(state.copyWith(foodList: updatedList));
  }

  Future<void> _onClearFood(ClearFood event, Emitter<RecentFoodState> emit) async {
    emit(state.copyWith(status: RecentFoodStatus.loading));

    try {
      await foodRepository.clearFood();

      emit(state.copyWith(status: RecentFoodStatus.success, foodList: const []));
    } catch (e) {
      emit(state.copyWith(status: RecentFoodStatus.failure, errorMessage: e.toString()));
    }
  }

  Future<void> _onDeleteFood(DeleteFood event, Emitter<RecentFoodState> emit) async {
    await foodRepository.deleteFood(event.meal);

    final updatedList = state.foodList.where((food) => food != event.meal).toList();

    emit(state.copyWith(status: RecentFoodStatus.success, foodList: updatedList));
    if (!event.meal.hasError || event.meal.isLoading) {
      await _decrementDailyUserData(
          event.meal.date!, event.meal.calories!.toDouble(), event.meal.carbs!, event.meal.protein!, event.meal.fat!);
    }
  }

  Future<void> _updateDailyUserData(DateTime date, double calories, double carbs, double protein, double fat) async {
    final currentDailyData = await getDailyUserDataUseCase(date);
    final updatedDailyData = currentDailyData.copyWith(
      consumedCalories: currentDailyData.consumedCalories + calories,
      consumedCarbs: currentDailyData.consumedCarbs + carbs,
      consumedProtein: currentDailyData.consumedProtein + protein,
      consumedFat: currentDailyData.consumedFat + fat,
    );
    await updateDailyUserDataUseCase(updatedDailyData);
  }

  Future<void> _decrementDailyUserData(DateTime date, double calories, double carbs, double protein, double fat) async {
    final currentDailyData = await getDailyUserDataUseCase(date);
    final updatedDailyData = currentDailyData.copyWith(
      consumedCalories: currentDailyData.consumedCalories - calories,
      consumedCarbs: currentDailyData.consumedCarbs - carbs,
      consumedProtein: currentDailyData.consumedProtein - protein,
      consumedFat: currentDailyData.consumedFat - fat,
    );
    await updateDailyUserDataUseCase(updatedDailyData);
  }
}
