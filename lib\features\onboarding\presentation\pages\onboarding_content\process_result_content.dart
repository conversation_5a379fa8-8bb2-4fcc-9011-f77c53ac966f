import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/utils/formulars.dart';
import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_template.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_metric_card.dart';
import 'package:cal/features/onboarding/presentation/widgets/progress_bar_card.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProcessResultContent extends StatefulWidget {
  const ProcessResultContent({super.key});

  @override
  State<ProcessResultContent> createState() => _ProcessingResultsContentState();
}

class _ProcessingResultsContentState extends State<ProcessResultContent> {
  final List<CardType> cardTypes = [CardType.cals, CardType.carbs, CardType.protien, CardType.fat];

  String getGoalTitle(Goal goal) {
    switch (goal) {
      case Goal.weightLoss:
        return LocaleKeys.onboarding_Your_goal_is_to_lose_weight.tr();
      case Goal.maintenance:
        return LocaleKeys.onboarding_Your_goal_is_to_maintain.tr();
      case Goal.weightGain:
        return LocaleKeys.onboarding_Your_goal_is_to_gain_weight.tr();
    }
  }

  Widget buildMacroRow(OnboardingState state, int startIndex, BuildContext ctx) {
    return Row(
      spacing: 10,
      children: List.generate(2, (i) {
        final index = startIndex + i;
        final value = switch (cardTypes[index]) {
          CardType.cals => "${state.cals!}",
          CardType.carbs => "${state.carbs!}",
          CardType.protien => "${state.protein!}",
          CardType.fat => "${state.fat!}",
        };
        return Expanded(
          child: OnboardingMetricCard(
            context: context,
            cardType: cardTypes[index],
            strokedWidth: 5,
            progress: 0.5,
            val: value,
          ),
        );
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        final isWeightLoss = state.goal == Goal.weightLoss;
        final goalAmount = (isWeightLoss ? state.weight! - state.targetWeight! : state.targetWeight! - state.weight!);

        final bmi = Formulars.calculateBMI(
          weightKg: state.weight!.toDouble(),
          heightCm: state.height!.toDouble(),
        );

        final age = Formulars.calculateAge(
          birthDay: state.birthDay!,
          birthMonth: state.birthMonth!,
          birthYear: state.birthYear!,
        );

        final bmr = Formulars.calculateBMR(
          weightKg: state.weight!.toDouble(),
          heightCm: state.height!.toDouble(),
          age: age,
          isMale: state.gender == Gender.male,
        );

        final tdee = Formulars.calculateTDEE(
          bmr: bmr,
          exerciseFrequency: state.exerciseFrequency!,
        );

        final proteinPercent = Formulars.proteinGramsToPercent(
          proteinGrams: state.protein!.toDouble(),
          totalCalories: state.cals!,
        );

        final carbsPercent = Formulars.proteinGramsToPercent(
          proteinGrams: state.carbs!.toDouble(),
          totalCalories: state.cals!,
        );

        final fatPercent = Formulars.proteinGramsToPercent(
          proteinGrams: state.fat!.toDouble(),
          totalCalories: state.cals!,
        );

        final healthScore = Formulars.calculateHealthScore(
          bmi: bmi,
          exerciseFrequency: state.exerciseFrequency!,
          weeklyGoalKg: state.weightChangeRate!,
          proteinPercent: proteinPercent,
          carbsPercent: carbsPercent,
          fatPercent: fatPercent,
          tdee: tdee,
          targetCalories: state.cals!,
          dietType: state.diet!,
        );

        return OnboardingScreenTemplate(
          title: state.goal != null ? getGoalTitle(state.goal!) : LocaleKeys.onboarding_Your_goal_is_to_maintain.tr(),
          contentWidgets: [
            if (state.goal != null && state.goal != Goal.maintenance && state.weight != null && state.targetWeight != null) ...[
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 51),
                decoration: BoxDecoration(
                  color: context.background,
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.all(4),
                child: Text(
                  "${goalAmount.toStringAsFixed(1)} ${LocaleKeys.onboarding_kg_by.tr()} ${Formulars.calculateWeightLossEndDate(
                    totalKgToLose: goalAmount,
                    kgPerWeek: state.weightChangeRate ?? 1,
                    locale: context.locale.languageCode,
                  )}",
                  textAlign: TextAlign.center,
                  style: context.textTheme.titleSmall!.copyWith(
                    fontWeight: FontWeight.w200,
                    color: Colors.grey.shade700,
                  ),
                ),
              ),
            ],
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: context.background,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.onboarding_daily_recommendations.tr(),
                    style: context.textTheme.titleMedium!.copyWith(
                      fontWeight: FontWeight.w400,
                      color: context.onSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    LocaleKeys.onboarding_you_can_edit_anytime.tr(),
                    style: context.textTheme.bodySmall!.copyWith(
                      fontWeight: FontWeight.w200,
                      color: context.onSecondary.withAlpha(70),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 14),
                  Column(
                    children: [
                      buildMacroRow(state, 0, context),
                      const SizedBox(height: 10),
                      buildMacroRow(state, 2, context),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ProgressBarCard(value: healthScore),
                ],
              ),
            ),
            const SizedBox(height: 50),
          ],
        );
      },
    );
  }
}
