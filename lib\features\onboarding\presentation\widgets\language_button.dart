import 'package:cal/common/consts/app_keys.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/utils/shared_preferences_helper.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class LangButton{

  static Widget buildLanguageButton(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.language, color: Colors.black),
      onPressed: () => showLanguageDialog(context),
    );
  }

  static void showLanguageDialog(BuildContext ctx) {
    showDialog(
      context: ctx,
      builder: (_) {
        return AlertDialog(
          title: Text(
            LocaleKeys.settings_language.tr(),
            style: ctx.textTheme.titleLarge!.copyWith(fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              buildLanguageOption(
                ctx,
                'English',
                '🇺🇸',
                ctx.locale == const Locale('en'),
                    () {
                  ctx.setLocale(const Locale('en'));
                  ShPH.saveData(key: AppKeys.appLanguage, value: 'en');
                  Navigator.pop(ctx);
                },
              ),
              const SizedBox(height: 12),
              buildLanguageOption(
                ctx,
                'العربية',
                '🇸🇾',
                ctx.locale == const Locale('ar'),
                    () {
                  ctx.setLocale(const Locale('ar'));
                  ShPH.saveData(key: AppKeys.appLanguage, value: 'ar');
                  Navigator.pop(ctx);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  static Widget buildLanguageOption(
      BuildContext context,
      String label,
      String flagEmoji,
      bool isSelected,
      VoidCallback onTap,
      ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? context.primaryColor.withAlpha(26) : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? context.primaryColor : Colors.grey.shade300,
          ),
        ),
        child: Row(
          children: [
            Text(
              flagEmoji,
              style: const TextStyle(fontSize: 24),
            ),
            const SizedBox(width: 12),
            Text(
              label,
              style: context.textTheme.bodyMedium!.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? context.primaryColor : Colors.black,
              ),
            ),
            const Spacer(),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: context.primaryColor,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

}
