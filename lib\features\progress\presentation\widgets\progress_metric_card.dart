import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:flutter/material.dart';

class ProgressMetricCard extends StatelessWidget {
  final String title;
  final String description;
  final String? buttonTitle;
  final String? icon;
  final Color progressColor;
  final double progress;
  final void Function()? onTap;
  final Duration animationDuration;

  const ProgressMetricCard({
    super.key,
    required this.title,
    required this.description,
    this.buttonTitle,
    this.icon,
    required this.progressColor,
    required this.progress,
    this.onTap,
    this.animationDuration = const Duration(milliseconds: 800),
  });

  @override
  Widget build(BuildContext context) {
    return AppGestureDetector(
      onTap: onTap,
      child: Container(
        height: context.screenWidth * .52,
        decoration: BoxDecoration(
          color: context.colorScheme.onPrimary,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha((0.2 * 255).round()),
              blurRadius: 50,
            ),
          ],
        ),
        padding: const EdgeInsetsDirectional.symmetric(vertical: 15),
        // margin: const EdgeInsets.all(5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 14.0),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  TweenAnimationBuilder<double>(
                    duration: animationDuration,
                    curve: Curves.easeInOut,
                    tween: Tween<double>(
                      begin: 0,
                      end: progress,
                    ),
                    builder: (context, value, _) {
                      return SizedBox(
                        width: context.screenWidth * .22,
                        height: context.screenWidth * .22,
                        child: CircularProgressIndicator(
                          value: value,
                          strokeWidth: 5,
                          backgroundColor: context.onSecondary.withAlpha(25),
                          valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                        ),
                      );
                    },
                  ),

                  // Icon in the center
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: context.onSecondary.withAlpha(25),
                      shape: BoxShape.circle,
                    ),
                    child: icon != null ? AppImage.asset(icon!) : null,
                  ),
                ],
              ),
            ),
            SizedBox(height: context.screenWidth * .02),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 14.0),
              child: Text(
                title,
                style: context.textTheme.labelMedium!.copyWith(color: onTap == null ? progressColor : Colors.black, fontWeight: FontWeight.w900),
              ),
            ),
            // Description
            SizedBox(height: context.screenWidth * .02),
            if (onTap == null)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 14.0),
                child: Text(
                  description,
                  style: context.textTheme.labelLarge!.copyWith(color: Colors.black, fontWeight: FontWeight.bold),
                ),
              ),

            if (onTap != null) Divider(color: progressColor),
            if (onTap != null)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 14.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      buttonTitle ?? "",
                      style: context.textTheme.labelMedium!.copyWith(color: progressColor, fontWeight: FontWeight.bold),
                    ),
                    Icon(Icons.arrow_forward_rounded, color: progressColor, size: 18)
                  ],
                ),
              )
          ],
        ),
      ),
    );
  }
}
