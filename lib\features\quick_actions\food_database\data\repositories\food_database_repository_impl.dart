import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/quick_actions/food_database/data/datasources/local_food_database_data_source.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/domain/repositories/food_database_repository.dart';
import 'package:injectable/injectable.dart';

@Injectable(as: FoodDatabaseRepository)
class FoodDatabaseRepositoryImpl implements FoodDatabaseRepository {
  final LocalFoodDatabaseDataSource localDataSource;

  const FoodDatabaseRepositoryImpl({required this.localDataSource});

  @override
  Future<void> saveMeal(DatabaseFoodModel meal) async {
    await localDataSource.saveMeal(meal);
  }

  @override
  Future<List<DatabaseFoodModel>> getDatabaseFood() async {
    return await localDataSource.getDatabaseMeals();
  }

  @override
  Future<List<DatabaseFoodModel>> getRecentFood() async {
    return await localDataSource.getRecentMealsFromSearch();
  }

  @override
  Future<List<DatabaseFoodModel>> getMyMeals() async {
    return await localDataSource.getCreatedMeals();
  }

  @override
  Future<List<DatabaseFoodModel>> getFavoriteFood() async {
    return await localDataSource.getFavoriteMeals();
  }

  @override
  Future<void> saveMealToLog(FoodModel food) async {
    return await localDataSource.saveMealToLog(food);
  }
}
