import 'dart:developer';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:cal/common/consts/app_consts.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/quick_actions/scan_food/domain/usecases/recognize_food_usecase.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

part 'scan_food_event.dart';
part 'scan_food_state.dart';

@injectable
class ScanFoodBloc extends Bloc<ScanFoodEvent, ScanFoodState> {
  final AnalyzeFoodUseCase analyzeFoodUseCase;
  final ImagePicker _imagePicker = ImagePicker();

  ScanFoodBloc({
    required this.analyzeFoodUseCase,
  }) : super(const ScanFoodState()) {
    on<ProcessCapturedImageEvent>(_onProcessCapturedImage);
    on<PickImageFromGalleryEvent>(_onPickImageFromGallery);
    on<RecognizeFoodEvent>(_onRecognizeFood);
    on<RetryRecognizeFoodEvent>(_onRetryRecognizeFood);
  }

  Future<void> _onProcessCapturedImage(ProcessCapturedImageEvent event, Emitter<ScanFoodState> emit) async {
    emit(state.copyWith(status: ScanFoodStatus.processing, capturedImage: event.imageFile));

    add(RecognizeFoodEvent(image: event.imageFile));
  }

  Future<void> _onPickImageFromGallery(PickImageFromGalleryEvent event, Emitter<ScanFoodState> emit) async {
    emit(state.copyWith(status: ScanFoodStatus.processing));

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image == null) {
        emit(state.copyWith(status: ScanFoodStatus.initial));
        return;
      }

      File imageFile = File(image.path);

      emit(state.copyWith(capturedImage: imageFile));
      add(RecognizeFoodEvent(image: imageFile));
      event.context?.pop();
    } catch (e) {
      emit(state.copyWith(
        status: ScanFoodStatus.error,
        errorMessage: 'Failed to pick image from gallery: $e',
      ));
    }
  }

  Future<void> _onRecognizeFood(RecognizeFoodEvent event, Emitter<ScanFoodState> emit) async {
    emit(state.copyWith(status: ScanFoodStatus.processing));

    log('Processing food recognition');

    var connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult.contains(ConnectivityResult.none)) {
      emit(state.copyWith(status: ScanFoodStatus.error, errorMessage: LocaleKeys.common_no_internet_connection.tr()));
      return;
    }

    final result = await analyzeFoodUseCase(analyzeFoodParams: await AnalyzeFoodParams.create(imageFile: event.image));

    await result.fold(
      (failure) async {
        emit(state.copyWith(status: ScanFoodStatus.error, errorMessage: failure.message));
      },
      (foodData) async {
        final savedPath = await saveImagePermanently(event.image);
        final recognizedFood = foodData.copyWith(imagePath: savedPath, isLoading: false);

        emit(state.copyWith(status: ScanFoodStatus.success, recognizedFood: recognizedFood, isRetry: state.isRetry));
      },
    );
  }

  Future<void> _onRetryRecognizeFood(RetryRecognizeFoodEvent event, Emitter<ScanFoodState> emit) async {
    if (state.capturedImage != null) {
      emit(state.copyWith(status: ScanFoodStatus.processing, isRetry: true));

      add(RecognizeFoodEvent(image: state.capturedImage!));
    } else {
      emit(state.copyWith(status: ScanFoodStatus.error, errorMessage: 'No image to retry'));
    }
  }
}

Future<String> saveImagePermanently(File image) async {
  final baseDir = await getApplicationDocumentsDirectory();
  final imageDir = Directory(p.join(baseDir.path, AppConsts.appName, 'images'));

  if (!await imageDir.exists()) {
    await imageDir.create(recursive: true);
  }

  final fileName = p.basename(image.path);
  final newPath = p.join(imageDir.path, fileName);

  final newImage = await File(image.path).copy(newPath);

  if (kDebugMode) {
    print('Image saved at: $newPath');
  }

  return newImage.path;
}
