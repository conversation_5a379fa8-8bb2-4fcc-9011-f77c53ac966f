import 'dart:developer';
import 'dart:io';

import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/bloc/scan_food_bloc.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:camera/camera.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';

class ScanCameraWidget extends StatefulWidget {
  const ScanCameraWidget({super.key});

  @override
  State<ScanCameraWidget> createState() => _ScanCameraWidgetState();
}

class _ScanCameraWidgetState extends State<ScanCameraWidget> with WidgetsBindingObserver {
  CameraController? _controller;
  bool _isInitialized = false;
  bool _isLoading = true;
  bool _hasPermission = false;
  bool _isFlashOn = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _checkPermissionAndInitCamera();
  }

  @override
  void dispose() {
    log("Disposing ScanCameraWidget");

    _disposeCamera();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_controller == null || !_controller!.value.isInitialized) return;

    if (state == AppLifecycleState.inactive || state == AppLifecycleState.paused) {
      _disposeCamera();
    } else if (state == AppLifecycleState.resumed) {
      _checkPermissionAndInitCamera();
    }
  }

  Future<void> _checkPermissionAndInitCamera() async {
    setState(() => _isLoading = true);

    final status = await Permission.camera.status;

    if (status.isGranted) {
      _hasPermission = true;
      await _initializeCamera();
    } else if (status.isDenied || status.isRestricted) {
      final requestStatus = await Permission.camera.request();
      _hasPermission = requestStatus.isGranted;

      if (_hasPermission) {
        await _initializeCamera();
      } else {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Camera permission is required';
        });
      }
    } else if (status.isPermanentlyDenied) {
      await openAppSettings();
      setState(() {
        _isLoading = false;
        _errorMessage = 'Please enable camera permissions in settings';
      });
    }
  }

  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'No cameras available';
        });
        return;
      }

      final controller = CameraController(
        cameras.first,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await controller.initialize();

      if (mounted) {
        setState(() {
          _controller = controller;
          _isInitialized = true;
          _isLoading = false;
          _errorMessage = null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to initialize camera: $e';
        });
      }
    }
  }

  Future<void> _disposeCamera() async {
    await _controller?.dispose();
  }

  Future<void> _toggleFlash() async {
    if (_controller != null && _controller!.value.isInitialized) {
      try {
        final newFlashMode = _isFlashOn ? FlashMode.off : FlashMode.torch;
        await _controller!.setFlashMode(newFlashMode);
        setState(() {
          _isFlashOn = !_isFlashOn;
        });
      } catch (e) {
        // Handle flash toggle error
      }
    }
  }

  Future<void> _captureImage() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      return;
    }

    try {
      setState(() => _isLoading = true);

      final XFile image = await _controller!.takePicture();

      if (mounted) {
        setState(() => _isLoading = false);

        context.read<ScanFoodBloc>().add(ProcessCapturedImageEvent(imageFile: File(image.path)));
        await _disposeCamera();
        if (mounted) context.pop();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to capture image: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          // Camera preview
          _buildCameraPreview(context: context),

          // Scanner frame corners
          Transform.translate(
            offset: const Offset(0, -40),
            child: Image.asset(Assets.imagesCameraFrame, scale: 1.2),
          ),

          // Top bar with close button
          Positioned(
            top: 50,
            left: 16,
            right: 16,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CircleAvatar(
                  backgroundColor: Colors.white,
                  child: IconButton(
                    icon: const Icon(Icons.close, color: Colors.black),
                    onPressed: () => context.pop(),
                  ),
                ),
              ],
            ),
          ),

          // Bottom action buttons
          Positioned(
            bottom: 20,
            left: 0,
            right: 0,
            child: Column(
              children: [
                // Action buttons row
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    spacing: 12,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      const SizedBox.shrink(),
                      _buildActionButton(
                        context: context,
                        icon: Icons.photo,
                        label: LocaleKeys.scan_gallery.tr(),
                        onTap: () async {
                          await _disposeCamera();
                          if (context.mounted) context.read<ScanFoodBloc>().add(PickImageFromGalleryEvent(context: context));
                        },
                      ),
                      // _buildActionButton(
                      //   context: context,
                      //   icon: Icons.info_outline,
                      //   label: 'المعلومات الغذائية',
                      //   onTap: () {},
                      // ),
                      // _buildActionButton(
                      //   context: context,
                      //   icon: Icons.qr_code_scanner,
                      //   label: 'باركود',
                      //   onTap: () {},
                      // ),
                      _buildActionButton(
                        context: context,
                        icon: Icons.camera_alt_outlined,
                        label: LocaleKeys.scan_scan_food.tr(),
                        onTap: () {},
                      ),
                      const SizedBox.shrink(),
                    ],
                  ),
                ),
                const SizedBox(height: 30),

                // Camera controls
                Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      child: CircleAvatar(
                        backgroundColor: Colors.white,
                        child: IconButton(
                          icon: Icon(
                            _isFlashOn ? Icons.flash_on : Icons.flash_off,
                            color: context.primaryColor,
                          ),
                          onPressed: _controller != null ? _toggleFlash : null,
                        ),
                      ),
                    ),
                    const Spacer(),
                    AppGestureDetector(
                      onTap: _isInitialized && !_isLoading ? _captureImage : null,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white,
                          border: Border.all(color: Colors.white, width: 3),
                        ),
                        child: Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: context.onPrimaryContainer),
                          ),
                        ),
                      ),
                    ),
                    const Spacer(),
                    const SizedBox(width: 70),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCameraPreview({required BuildContext context}) {
    if (!_hasPermission) {
      return Container(
        color: Colors.black,
        child: Center(
          child: CircularProgressIndicator(color: context.primaryColor),
        ),
      );
    }

    if (_errorMessage != null) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _errorMessage!,
                style: const TextStyle(color: Colors.white, fontSize: 18),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _checkPermissionAndInitCamera,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (_controller != null && _controller!.value.isInitialized) {
      return AspectRatio(
        aspectRatio: _controller!.value.aspectRatio,
        child: CameraPreview(_controller!),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return AppGestureDetector(
      onTap: onTap,
      child: Container(
        height: 75,
        width: 80,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: context.primaryColor),
            const SizedBox(height: 5),
            Text(
              textAlign: TextAlign.center,
              label,
              style: context.textTheme.labelSmall!.copyWith(color: context.onSecondary, fontSize: 9, fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ),
    );
  }
}
